// Code generated by gotmpl. DO NOT MODIFY.
// source: internal/shared/otlp/otlpmetric/otest/client_test.go.tmpl

// Copyright The OpenTelemetry Authors
// SPDX-License-Identifier: Apache-2.0

package otest

import (
	"context"
	"testing"

	"go.opentelemetry.io/otel"
	"{{ .internalImportPath }}"
	"go.opentelemetry.io/otel/sdk/metric"
	"go.opentelemetry.io/otel/sdk/metric/metricdata"
	cpb "go.opentelemetry.io/proto/otlp/collector/metrics/v1"
	mpb "go.opentelemetry.io/proto/otlp/metrics/v1"
)

type client struct {
	rCh     <-chan ExportResult
	storage *Storage
}

func (c *client) Temporality(k metric.InstrumentKind) metricdata.Temporality {
	return metric.DefaultTemporalitySelector(k)
}

func (c *client) Aggregation(k metric.InstrumentKind) metric.Aggregation {
	return metric.DefaultAggregationSelector(k)
}

func (c *client) Collect() *Storage {
	return c.storage
}

func (c *client) UploadMetrics(ctx context.Context, rm *mpb.ResourceMetrics) error {
	c.storage.Add(&cpb.ExportMetricsServiceRequest{
		ResourceMetrics: []*mpb.ResourceMetrics{rm},
	})
	if c.rCh != nil {
		r := <-c.rCh
		if r.Response != nil && r.Response.GetPartialSuccess() != nil {
			msg := r.Response.GetPartialSuccess().GetErrorMessage()
			n := r.Response.GetPartialSuccess().GetRejectedDataPoints()
			if msg != "" || n > 0 {
				otel.Handle(internal.MetricPartialSuccessError(n, msg))
			}
		}
		return r.Err
	}
	return ctx.Err()
}

func (c *client) ForceFlush(ctx context.Context) error { return ctx.Err() }
func (c *client) Shutdown(ctx context.Context) error   { return ctx.Err() }

func TestClientTests(t *testing.T) {
	factory := func(rCh <-chan ExportResult) (Client, Collector) {
		c := &client{rCh: rCh, storage: NewStorage()}
		return c, c
	}

	t.Run("Integration", RunClientTests(factory))
}
