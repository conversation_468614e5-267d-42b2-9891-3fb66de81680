# HELP bar_bytes meter b bar
# TYPE bar_bytes gauge
bar_bytes{otel_scope_name="ma",otel_scope_schema_url="",otel_scope_version="v0.1.0",type="bar"} 100
bar_bytes{otel_scope_name="mb",otel_scope_schema_url="",otel_scope_version="v0.1.0",type="bar"} 100
# HELP target_info Target metadata
# TYPE target_info gauge
target_info{"service.name"="prometheus_test","telemetry.sdk.language"="go","telemetry.sdk.name"="opentelemetry","telemetry.sdk.version"="latest"} 1
