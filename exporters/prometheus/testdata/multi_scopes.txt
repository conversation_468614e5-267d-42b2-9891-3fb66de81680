# HELP bar_seconds_total meter bar counter
# TYPE bar_seconds_total counter
bar_seconds_total{otel_scope_name="meterbar",otel_scope_schema_url="",otel_scope_version="v0.1.0",type="bar"} 200
# HELP foo_seconds_total meter foo counter
# TYPE foo_seconds_total counter
foo_seconds_total{otel_scope_name="meterfoo",otel_scope_schema_url="",otel_scope_version="v0.1.0",type="foo"} 100
# HELP target_info Target metadata
# TYPE target_info gauge
target_info{"service.name"="prometheus_test","telemetry.sdk.language"="go","telemetry.sdk.name"="opentelemetry","telemetry.sdk.version"="latest"} 1
